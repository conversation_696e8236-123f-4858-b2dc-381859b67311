import { IconBeaker, IconCode } from '@hi-design/ui-icons';
import { INFO_DEBUG_TEXT_MAP } from './I18nTextMap';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';

export const INFO_DEBUG_TAB = {
  LIVE_DEBUG: {
    name: INFO_DEBUG_TEXT_MAP.LIVE_DEBUG,
    key: 'liveDebug',
    icon: IconCode,
  },
  LOG_ANALYSIS: {
    name: INFO_DEBUG_TEXT_MAP.LOG_ANALYSIS,
    key: 'logAnalysis',
    icon: IconBeaker,
  },
};

export enum ServiceRegion {
  ROW = 'ROW',
  GCP = 'GCP',
  TTP = 'TTP',
}

export const LOG_REGION_OPTIONS = [
  { label: ServiceRegion.ROW, value: ServiceRegion.ROW, disabled: false },
  { label: ServiceRegion.GCP, value: ServiceRegion.GCP, disabled: true },
  { label: ServiceRegion.TTP, value: ServiceRegion.TTP, disabled: true },
];

export enum ReqExecNodeStatus {
  Unknown = 0,
  Success = 1,
  Fail = 2,
}

export const REQ_EXEC_NODE_MAP = {
  [info_service.ErrorParty.UPSTREAM]: INFO_DEBUG_TEXT_MAP.CALLER,
  [info_service.ErrorParty.DATAMARKET]: INFO_DEBUG_TEXT_MAP.DATA_MARKET,
  [info_service.ErrorParty.DMSCRIPT]: INFO_DEBUG_TEXT_MAP.SCRIPT,
  [info_service.ErrorParty.DOWNSTREAMRPC]: INFO_DEBUG_TEXT_MAP.DOWNSTREAM,
};

export const REQ_EXEC_NODE_STATUS: Record<
  ReqExecNodeStatus,
  'wait' | 'process' | 'finish' | 'error' | 'warning'
> = {
  [ReqExecNodeStatus.Unknown]: 'wait',
  [ReqExecNodeStatus.Success]: 'finish',
  [ReqExecNodeStatus.Fail]: 'error',
};

export enum ArgosLogLevel {
  DEBUG = 'Debug',
  INFO = 'Info',
  WARN = 'Warn',
  ERROR = 'Error',
  FATAL = 'Fatal',
}

export const LIVE_DEBUG_PSM = 'ies.kefu.tool_gateway';
