export const getCurrentTime = () => Date.now();

export const getDefaultCurrentTime = (time: number) => time ?? Date.now();

/**
 * Extracts timestamp from a log ID and checks if it's within the specified minutes from current time
 * @param logId The log ID string (format: YYYYMMDDHHMMSS...)
 * @param withinMinutes Number of minutes to check against
 * @returns Boolean indicating if log timestamp is within the specified minutes
 */
export const isLogTimestampWithinMinutes = (
  logId: string,
  withinMinutes = 5,
): boolean => {
  if (!logId || logId.length < 14) {
    return false;
  }

  try {
    // Extract YYYYMMDDHHMMSS from the beginning of the log ID
    const timestampStr = logId.substring(0, 14);

    // Parse the timestamp parts
    const year = parseInt(timestampStr.substring(0, 4));
    const month = parseInt(timestampStr.substring(4, 6)) - 1; // JS months are 0-indexed
    const day = parseInt(timestampStr.substring(6, 8));
    const hour = parseInt(timestampStr.substring(8, 10));
    const minute = parseInt(timestampStr.substring(10, 12));
    const second = parseInt(timestampStr.substring(12, 14));

    // Create Date object with UTC time and get timestamp
    const logTimestamp = Date.UTC(year, month, day, hour, minute, second);
    const currentTime = getCurrentTime();

    // Check if the log timestamp is within the specified minutes
    const diffInMs = currentTime - logTimestamp;
    const diffInMinutes = diffInMs / (1000 * 60);

    return diffInMinutes <= withinMinutes;
  } catch (error) {
    console.error('Error parsing log timestamp:', error);
    return false;
  }
};
