import * as React from 'react';
import { Route, Switch } from '@edenx/runtime/router-v5';
import loadableComp from '@components/global/loading';
import RouteRender from '@components/global/RouteRender';
import { RouterPath } from './const';

const { lazy } = React;

const APIList = loadableComp(lazy(() => import('@pages/API/List')));
const INFOList = loadableComp(lazy(() => import('@pages/INFO/List')));
const INFODebug = loadableComp(lazy(() => import('@pages/INFO/debug')));
const APIDetail = loadableComp(lazy(() => import('@pages/API/Detail')));
const INFODetail = loadableComp(lazy(() => import('@pages/INFO/Detail')));
const NoMatch = loadableComp(lazy(() => import('@pages/no_match')));

const routeConfig = [
  {
    path: RouterPath.INFO_LIST,
    component: INFOList,
  },
  {
    path: RouterPath.INFO_DETAIL,
    component: INFODetail,
  },
  {
    path: RouterPath.INFO_DEBUG,
    component: INFODebug,
  },
  {
    path: RouterPath.API_LIST,
    component: APIList,
  },
  {
    path: RouterPath.API_DETAIL,
    component: APIDetail,
  },
];

export default function AppRouter(): React.FunctionComponentElement<React.ReactNode> {
  return (
    <Switch>
      <RouteRender routeConfig={routeConfig} />
      <Route component={NoMatch} />
    </Switch>
  );
}
