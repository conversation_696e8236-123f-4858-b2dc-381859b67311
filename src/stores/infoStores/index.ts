import { InfoDetail } from '@/types/info-debug';
import { makeAutoObservable } from 'mobx';

export interface IInfoStore {
  currentInfo: InfoDetail | null;
  setCurrentInfo: (info: InfoDetail) => void;
}

export class InfoStore implements IInfoStore {
  rootStore: any;
  currentInfo: InfoDetail | null = null;

  constructor(rootStore: any) {
    makeAutoObservable(this, {}, { autoBind: true });
    this.rootStore = rootStore;
  }

  setCurrentInfo(info: InfoDetail | null) {
    this.currentInfo = info;
  }
}
