import { FC, useMemo, useState } from 'react';
import styles from './index.module.scss';
import {
  IconAlertTriangle,
  IconTickCircle,
  IconUploadError,
} from '@hi-design/ui-icons';
import { List, Tooltip, Typography } from '@hi-design/ui';
import classNames from 'classnames';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';

export interface IInfoValueTableProps {
  activeInfoKey?: string; // 当前页面携带的info key
  infoKeyResults: info_service.InfoKeyResult[];
  infoKeyDiff: string[];
}

enum InfoReturnStatus {
  Success = 'success',
  Warning = 'warning',
  Error = 'error',
}
export const InfoValueTable: FC<IInfoValueTableProps> = props => {
  const { activeInfoKey, infoKeyResults, infoKeyDiff } = props;
  const [selectedInfoKey, setSelectedInfoKey] = useState<string>(
    activeInfoKey || '',
  );

  const compareStatus = (
    a: { status: InfoReturnStatus },
    b: { status: InfoReturnStatus },
  ) => {
    const priority = {
      [InfoReturnStatus.Error]: 0,
      [InfoReturnStatus.Warning]: 1,
      [InfoReturnStatus.Success]: 2,
    };

    return priority[a.status] - priority[b.status];
  };

  const infoValues = useMemo(() => {
    const res = infoKeyDiff?.map(key => ({
      infoKey: key,
      infoValue: '',
      status: InfoReturnStatus.Error,
    }));
    const formatInfoKeyResults = infoKeyResults?.map(item => ({
      infoKey: item.InfoKey,
      infoValue: item.InfoValue,
      status: item.InfoValue
        ? InfoReturnStatus.Success
        : InfoReturnStatus.Warning,
    }));
    return res.concat(formatInfoKeyResults).sort((a, b) => {
      if (a.status !== b.status) {
        return compareStatus(a, b);
      }
      return a.infoKey.localeCompare(b.infoKey);
    });
  }, [infoKeyDiff, infoKeyResults]);

  const getStatusIcon = (status: InfoReturnStatus) => {
    if (status === InfoReturnStatus.Success) {
      return (
        <IconTickCircle className={`${styles.statusIcon} ${styles.success}`} />
      );
    }
    if (status === InfoReturnStatus.Warning) {
      return (
        <IconAlertTriangle
          className={`${styles.statusIcon} ${styles.warning}`}
        />
      );
    }
    if (status === InfoReturnStatus.Error) {
      return (
        <IconUploadError className={`${styles.statusIcon} ${styles.error}`} />
      );
    }
    return null;
  };

  const getToolTipContent = (status: InfoReturnStatus) => {
    if (status === InfoReturnStatus.Success) {
      return INFO_DEBUG_TEXT_MAP.RESPONSE_SUCCESS_WITH_VALUE;
    }
    if (status === InfoReturnStatus.Warning) {
      return INFO_DEBUG_TEXT_MAP.RESPONSE_SUCCESS_NO_VALUE;
    }
    if (status === InfoReturnStatus.Error) {
      return INFO_DEBUG_TEXT_MAP.RESPONSE_NOT_RETURNED;
    }
    return null;
  };

  const selectedInfoKeyValue = useMemo(() => {
    if (!selectedInfoKey) {
      return INFO_DEBUG_TEXT_MAP.SELECT_INFO_KEY_TO_VIEW_VALUE;
    }
    const selectedItem = infoValues
      .filter(val => val.infoKey === selectedInfoKey)
      .at(0);

    if (!selectedItem) {
      return INFO_DEBUG_TEXT_MAP.INFO_KEY_NOT_FOUND(selectedInfoKey);
    }

    return (
      infoValues.filter(val => val.infoKey === selectedInfoKey).at(0)
        ?.infoValue || INFO_DEBUG_TEXT_MAP.NO_VALUE_RETURNED
    );
  }, [selectedInfoKey, infoValues]);

  const handleTableItemClick = (
    e: React.MouseEvent<HTMLLIElement>,
    infoKey: string,
  ) => {
    e.stopPropagation();
    setSelectedInfoKey(infoKey);
  };

  const renderItem = (item: {
    infoKey: string;
    infoValue: string;
    status: string;
  }) => (
    <List.Item
      key={item.infoKey}
      className={classNames(styles.infoKeyItem, {
        [styles.highlight]: item.infoKey === activeInfoKey,
        [styles.active]: item.infoKey === selectedInfoKey,
      })}
      onClick={e => {
        handleTableItemClick(e, item.infoKey);
      }}
    >
      <Tooltip content={getToolTipContent(item.status as InfoReturnStatus)}>
        {getStatusIcon(item.status as InfoReturnStatus)}
      </Tooltip>
      <Typography.Text
        className={styles.infoKeyText}
        ellipsis={{ showTooltip: true, pos: 'middle' }}
        type={selectedInfoKey === item.infoKey ? 'primary' : 'secondary'}
      >
        {item.infoKey}
      </Typography.Text>
    </List.Item>
  );

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <Typography.Text strong className={styles.header}>
          {INFO_DEBUG_TEXT_MAP.INFO_KEYS}
        </Typography.Text>
        <List
          className={styles.infoKeyList}
          size="small"
          bordered
          dataSource={infoValues}
          renderItem={renderItem}
        />
      </div>

      <div className={styles.main}>
        <Typography.Text strong className={styles.header}>
          {INFO_DEBUG_TEXT_MAP.RESPONSE_DATA}
        </Typography.Text>
        <div className={styles.infoValue}>
          <Typography.Paragraph>{selectedInfoKeyValue}</Typography.Paragraph>
        </div>
      </div>
    </div>
  );
};
