import {
  REQ_EXEC_NODE_MAP,
  REQ_EXEC_NODE_STATUS,
  ReqExecNodeStatus,
} from '@/common/constants/info';
import { Steps } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { NoData } from '../no-data';

export interface ExecutionNodeOverview {
  status: ReqExecNodeStatus;
}

export interface ILogOverviewProps {
  executionNodes?: Partial<
    Record<info_service.ErrorParty, info_service.ErrorInfo>
  >;
  callerPSM: string;
}

export const LogOverview: FC<ILogOverviewProps> = props => {
  const { executionNodes, callerPSM } = props;
  const getStatus = (node: info_service.ErrorParty) => {
    if (!executionNodes) {
      return 'wait';
    }
    return (
      REQ_EXEC_NODE_STATUS[
        (executionNodes?.[node]?.status as ReqExecNodeStatus) || 0
      ] || 'wait'
    );
  };
  return (
    <div className={styles.container}>
      {executionNodes ? (
        <Steps type="basic" current={1} onChange={i => console.log(i)}>
          <Steps.Step
            key={info_service.ErrorParty.UPSTREAM}
            title={REQ_EXEC_NODE_MAP[info_service.ErrorParty.UPSTREAM]}
            status={getStatus(info_service.ErrorParty.UPSTREAM)}
            description={`PSM: ${callerPSM || '-'}`}
          />
          <Steps.Step
            key={info_service.ErrorParty.DATAMARKET}
            title={REQ_EXEC_NODE_MAP[info_service.ErrorParty.DATAMARKET]}
            status={getStatus(info_service.ErrorParty.DATAMARKET)}
          />
          <Steps.Step
            key={info_service.ErrorParty.DMSCRIPT}
            title={REQ_EXEC_NODE_MAP[info_service.ErrorParty.DMSCRIPT]}
            status={getStatus(info_service.ErrorParty.DMSCRIPT)}
          />
          <Steps.Step
            key={info_service.ErrorParty.DOWNSTREAMRPC}
            title={REQ_EXEC_NODE_MAP[info_service.ErrorParty.DOWNSTREAMRPC]}
            status={getStatus(info_service.ErrorParty.DOWNSTREAMRPC)}
          />
        </Steps>
      ) : (
        <NoData />
      )}
    </div>
  );
};
