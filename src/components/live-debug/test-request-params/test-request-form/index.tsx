import { FC, useState, useMemo, ReactNode, useRef, useEffect } from 'react';
import { Button, Descriptions, Form, Popover, Typography } from '@hi-design/ui';
import {
  IconEdit,
  IconHelpCircle as IconQuestionCircle,
} from '@hi-design/ui-icons';
import styles from './index.module.scss';
import { FormApi } from '@hi-design/ui/es/components/form/types';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { isEmpty } from 'lodash-es';
import { NoData } from '@/components/log-analysis/no-data';

export interface TestRequestFormProps {
  requestDataConfig: Record<string, info_service.InfoField>;
  initRequestDataFormValues: Record<string, string>;
  isResultLoading: boolean;
  isRefreshLoading: boolean;
  onRequestDataFormChange: (values: Record<string, string>) => void;
  handleExecuteRequest: (values: Record<string, string>) => void;
}

export const TestRequestForm: FC<TestRequestFormProps> = ({
  requestDataConfig,
  initRequestDataFormValues,
  isResultLoading,
  isRefreshLoading,
  onRequestDataFormChange,
  handleExecuteRequest,
}) => {
  // State to track which fixed fields are currently editable
  const [editableFixedFields, setEditableFixedFields] = useState<
    Record<string, boolean>
  >({});

  const formAPIRef = useRef<FormApi<Record<string, string>> | null>(null);

  // 每次重新拉数据时同步更新表单数据
  useEffect(() => {
    formAPIRef.current?.setValues(initRequestDataFormValues);
  }, [initRequestDataFormValues]);

  // Toggle editable state for a fixed field
  const toggleFieldEditable = (fieldName: string) => {
    setEditableFixedFields(prev => ({
      ...prev,
      [fieldName]: !(prev?.[fieldName] ?? false),
    }));
  };

  const handleRunData = async () => {
    const res = await formAPIRef.current?.validate();
    handleExecuteRequest(res ?? {});
  };

  // Group fields by fixed status
  const groupedFields = useMemo(() => {
    const normalFields: string[] = [];
    const fixedFields: string[] = [];

    // Get all field keys and sort them by required status (required fields first)
    const sortedFieldKeys = Object.keys(requestDataConfig).sort((a, b) => {
      const fieldA = requestDataConfig?.[a];
      const fieldB = requestDataConfig?.[b];

      // Sort by required status: required fields (true) come first
      if (fieldA?.fieldIsRequired && !fieldB?.fieldIsRequired) {
        return -1;
      }
      if (!fieldA?.fieldIsRequired && fieldB?.fieldIsRequired) {
        return 1;
      }
      return 0; // Keep original order for fields with same required status
    });

    sortedFieldKeys.forEach(key => {
      normalFields.push(key);
    });

    return { normalFields, fixedFields };
  }, [requestDataConfig]);

  const renderLabel = (field: info_service.InfoField): ReactNode => {
    const descData = [
      {
        key: INFO_DEBUG_TEXT_MAP.FIELD_NAME,
        value: field?.field,
      },
      {
        key: INFO_DEBUG_TEXT_MAP.FIELD_DISPLAY_NAME,
        value: field?.fieldName,
      },
      {
        key: INFO_DEBUG_TEXT_MAP.FIELD_TYPE,
        value: field?.fieldType,
      },
      {
        key: INFO_DEBUG_TEXT_MAP.FIELD_DESC,
        value: field?.fieldDesc,
      },
    ];
    return (
      <div className={styles.label}>
        <Typography.Text
          ellipsis={{ showTooltip: true }}
          className={styles.labelText}
        >
          {field?.fieldName || field?.field}
        </Typography.Text>
        <Popover
          content={
            <Descriptions
              align="center"
              layoutInfo={{
                titleWidth: 100,
              }}
              className={styles.popoverContent}
              data={descData}
            />
          }
          position="right"
        >
          <IconQuestionCircle size="small" className={styles.helpIcon} />
        </Popover>
      </div>
    );
  };

  return (
    <div className={styles.formContainer}>
      {!isEmpty(requestDataConfig) ? (
        <Form
          disabled={isResultLoading || isRefreshLoading}
          labelWidth={140}
          labelPosition="left"
          labelAlign="right"
          initValues={initRequestDataFormValues}
          onValueChange={values => {
            onRequestDataFormChange(values as Record<string, string>);
          }}
          getFormApi={formApi => (formAPIRef.current = formApi)}
        >
          <div className={styles.formScroll}>
            {/* Normal fields */}
            {groupedFields?.normalFields?.map(fieldKey => {
              const field = requestDataConfig?.[fieldKey];
              return (
                <Form.Input
                  rules={[
                    {
                      required: false,
                      // required: field?.fieldIsRequired, // 暂时不做必填校验，待必填字段配置正确后再校验
                      message: COMMON_FE_TEXT_MAP.REQUIRED_FIELD(
                        field?.fieldName || field?.field || '',
                      ),
                    },
                  ]}
                  fieldClassName={styles.formField}
                  key={fieldKey}
                  field={fieldKey}
                  label={{
                    text: renderLabel(field as info_service.InfoField),
                  }}
                />
              );
            })}

            {/* Divider if there are fixed fields */}
            {groupedFields?.fixedFields?.length > 0 && (
              <div className={styles.fixedFieldsDivider}>
                <Typography.Text type="secondary">
                  {INFO_DEBUG_TEXT_MAP.FIXED_FIELDS}
                </Typography.Text>
              </div>
            )}

            {/* Fixed fields */}
            {groupedFields?.fixedFields?.map(fieldKey => {
              const field = requestDataConfig?.[fieldKey];
              const isEditable = editableFixedFields?.[fieldKey];

              return (
                <Form.Input
                  key={fieldKey}
                  fieldClassName={styles.formField}
                  field={fieldKey}
                  disabled={!isEditable}
                  rules={[
                    {
                      required: false,
                      // required: field?.fieldIsRequired, // 暂时不做必填校验，待必填字段配置正确后再校验
                      message: COMMON_FE_TEXT_MAP.REQUIRED_FIELD(
                        field?.fieldName || field?.field || '',
                      ),
                    },
                  ]}
                  label={{
                    text: renderLabel(field as info_service.InfoField),
                  }}
                  suffix={
                    <Button
                      icon={<IconEdit />}
                      type="tertiary"
                      size="small"
                      onClick={() => toggleFieldEditable(fieldKey)}
                    />
                  }
                />
              );
            })}
          </div>
          <div className={styles.formActions}>
            <Button
              disabled={isEmpty(requestDataConfig) || isRefreshLoading}
              type="primary"
              theme="solid"
              loading={isResultLoading}
              onClick={handleRunData}
            >
              {INFO_DEBUG_TEXT_MAP.RUN_DATA}
            </Button>
          </div>
        </Form>
      ) : (
        <NoData />
      )}
    </div>
  );
};

export default TestRequestForm;
