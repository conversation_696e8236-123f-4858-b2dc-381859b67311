import {
  Button,
  Collapsible,
  Form,
  Space,
  Tooltip,
  Typography,
} from '@hi-design/ui';
import {
  IconChevronDown,
  IconChevronUp,
  IconHelpCircle,
  IconStop,
} from '@hi-design/ui-icons';
import { FC, useRef, useState } from 'react';
import { LOG_REGION_OPTIONS, ServiceRegion } from '@/common/constants/info';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';
import styles from './index.module.scss';
import { FormApi } from '@hi-design/ui/es/components/form/types';

export interface DataFromLogIdForm {
  logID: string;
  region: ServiceRegion;
}

export interface LogDataFormProps {
  initialValues?: DataFromLogIdForm;
  isLogLoading: boolean;
  onDataLogFormChange: (values: DataFromLogIdForm) => void;
  handleGetRequestDataFromLogId: () => void;
  handleCancelGetDataFromLogId: () => void;
}

const LogDataForm: FC<LogDataFormProps> = ({
  initialValues = { logID: '', region: ServiceRegion.ROW },
  isLogLoading,
  onDataLogFormChange,
  handleGetRequestDataFromLogId,
  handleCancelGetDataFromLogId,
}) => {
  const formAPIRef = useRef<FormApi<DataFromLogIdForm>>();
  const { Option } = Form.Select;
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <div className={styles.container}>
      <div
        className={styles.collapseHeader}
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className={styles.headerText}>
          <Typography.Title heading={6} className={styles.title}>
            {INFO_DEBUG_TEXT_MAP.GET_DATA_FROM_LOG}
            <Tooltip content={INFO_DEBUG_TEXT_MAP.GET_DATA_FROM_LOG_DESC}>
              <IconHelpCircle
                className={`${styles.helpIcon} ${styles.titleIcon}`}
              />
            </Tooltip>
          </Typography.Title>
        </div>
        {isCollapsed ? <IconChevronDown /> : <IconChevronUp />}
      </div>
      <Collapsible isOpen={!isCollapsed}>
        <div className={styles.formContainer}>
          <Form
            layout="horizontal"
            initValues={initialValues}
            disabled={isLogLoading}
            onValueChange={values =>
              onDataLogFormChange(values as DataFromLogIdForm)
            }
            onSubmit={handleGetRequestDataFromLogId}
            getFormApi={formApi => (formAPIRef.current = formApi)}
          >
            <Form.Input
              field="logID"
              label={{
                text: 'Log ID',
                extra: (
                  <Tooltip content={INFO_DEBUG_TEXT_MAP.INPUT_LOG_ID_TIP}>
                    <IconHelpCircle size="small" className={styles.helpIcon} />
                  </Tooltip>
                ),
              }}
              rules={[
                {
                  required: true,
                  message: COMMON_FE_TEXT_MAP.REQUIRED_FIELD('Log ID'),
                },
              ]}
              style={{ width: 350 }}
            />
            <Form.Select
              field="region"
              label={{
                text: INFO_DEBUG_TEXT_MAP.DEPLOY_REGION,
                extra: (
                  <Tooltip content={INFO_DEBUG_TEXT_MAP.SELECT_LOG_REGION_TIP}>
                    <IconHelpCircle size="small" className={styles.helpIcon} />
                  </Tooltip>
                ),
              }}
              style={{ width: 120 }}
              initValue={ServiceRegion.ROW}
            >
              {LOG_REGION_OPTIONS.map(option => (
                <Option
                  key={option?.value}
                  value={option?.value}
                  disabled={option?.disabled}
                >
                  {option?.label}
                </Option>
              ))}
            </Form.Select>
          </Form>
          <Space className={styles.formActions}>
            <Button
              loading={isLogLoading}
              theme="solid"
              onClick={() => formAPIRef.current?.submitForm()}
            >
              {INFO_DEBUG_TEXT_MAP.GET_REQUEST_DATA_FROM_LOG}
            </Button>
            {isLogLoading ? (
              <Button
                theme="borderless"
                type="danger"
                icon={<IconStop />}
                onClick={handleCancelGetDataFromLogId}
              >
                {COMMON_FE_TEXT_MAP.Cancel}
              </Button>
            ) : null}
          </Space>
        </div>
      </Collapsible>
    </div>
  );
};

export default LogDataForm;
