import { useCallback, useMemo, useRef, useState } from 'react';
import { LOG_REGION_OPTIONS, ServiceRegion } from '@/common/constants/info';
import { LogAnalysisForm } from '@/components/log-analysis/log-form';
import { useHistory } from '@edenx/plugin-router-v5/runtime';
import queryString from 'query-string';
import { LogRequest } from '@/components/log-analysis/log-request-data';
import { format } from 'date-fns';
import { InfoDetail } from '@/types/info-debug';
import { useStores } from '@/stores';
import { infoServiceRpcService } from '@/common/http/infoServiceRPCService';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { Toast } from '@hi-design/ui';
import { ErrorResponse, isRequestCancelled } from '@/common/http';
import { isLogTimestampWithinMinutes } from '@/common/utils/time';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { RouterPath } from '@/const';

export interface UseInfoLogAnalysisResult {
  isSearching: boolean;
  hasSearchReturned: boolean;
  searchStatus: {
    success: boolean;
    reason: string;
  };
  formValues: LogAnalysisForm;
  logData: info_service.CheckDMLogMileStoneResponse | null;
  requestData: LogRequest[];
  argosLink?: string;
  currentInfo: InfoDetail | null;
  handleSearchValueChange: (values: LogAnalysisForm) => void;
  handleStopSearch: () => void;
  onSubmit: () => void;
}

// eslint-disable-next-line max-lines-per-function
const useInfoLogAnalysis = (): UseInfoLogAnalysisResult => {
  const history = useHistory();
  const query = queryString.parse(history.location.search) || {};
  const { logID, region, psm } = query;
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearchReturned, setHasSearchReturned] = useState(false);
  const [searchStatus, setSearchStatus] = useState<{
    success: boolean;
    reason: string;
  }>({ success: true, reason: '' });
  const [logData, setLogData] =
    useState<info_service.CheckDMLogMileStoneResponse | null>(null);
  const abortController = useRef<AbortController>();
  const { info } = useStores();
  const { currentInfo } = info || {};

  const resetResult = () => {
    setLogData(null);
    setHasSearchReturned(false);
  };

  const handleSearchLog = useCallback(async () => {
    resetResult();
    if (abortController.current) {
      return;
    }
    const controller = new AbortController();
    abortController.current = controller;
    try {
      setIsSearching(true);

      const res = await infoServiceRpcService.CheckDMLogMileStone(
        {
          logId: logID as string,
          dataCenter: region as string,
          upstreamPsm: psm as string,
        },
        { signal: controller.signal },
      );

      setLogData(res);
      setHasSearchReturned(true);
      setSearchStatus({
        success: res.logFindSuccess || false,
        reason: isLogTimestampWithinMinutes(logID as string)
          ? INFO_DEBUG_TEXT_MAP.LOG_FETCH_FAIL_DELAY
          : '',
      });
    } catch (error) {
      setHasSearchReturned(false);

      // Check if the request was cancelled
      if (isRequestCancelled(error)) {
        Toast.info('Log search was cancelled by user');
        console.log('Log search cancelled by user');
        return;
      }

      // Handle actual errors
      const err = error as ErrorResponse;
      Toast.error(
        `Failed to search log. ${err.statusMsg || ''} ${err.error || ''}`,
      );
      console.error(error);
    } finally {
      abortController.current = undefined;
      setIsSearching(false);
    }
  }, [logID, region, psm]);

  const requestData = useMemo(() => {
    if (!logData?.queryResults?.length) {
      return [];
    }

    const formatted = logData.queryResults
      ?.sort((a, b) => a.requestId?.localeCompare(b?.requestId || '') || 0)
      ?.map((item, idx) => ({
        key: String(item.requestId) || String(idx),
        tabDesc: `${
          item.timeStamp
            ? format(
                new Date(Number(item.timeStamp.slice(0, -3))),
                'yyyy-MM-dd HH:mm:ss.SSS',
              )
            : '-'
        }`,
        requestParams: Object.entries(item.requestParams || {})
          .map(([key, value]) => ({
            title: key,
            value: String(value),
          }))
          .sort((a, b) => a.title.localeCompare(b.title)),
        infoKeyResults: item.infoKeyResults || [],
        infoKeyDiff:
          item.requestInfokeys?.filter(
            out => !item.infoKeyResults?.some(inItem => inItem.InfoKey === out),
          ) || [],
      }));

    return formatted;
  }, [logData]);

  return {
    isSearching,
    hasSearchReturned,
    searchStatus,
    formValues: {
      logID: logID as string,
      region: LOG_REGION_OPTIONS.filter(
        item => item?.value === ServiceRegion.ROW,
      )?.[0]?.value,
      psm: psm as string,
    },
    logData,
    requestData,
    argosLink: logData?.logUrl || '',
    currentInfo,
    handleSearchValueChange: (values: LogAnalysisForm) => {
      const updatedQuery = {
        ...query,
        logID: values.logID,
        region: values.region,
        psm: values.psm,
      };

      const stringfied = queryString.stringify(updatedQuery);
      history.replace(`${RouterPath.INFO_DEBUG}?${stringfied}`);
    },
    handleStopSearch: () => {
      abortController.current?.abort();
      setIsSearching(false);
    },
    onSubmit: handleSearchLog,
  };
};
export default useInfoLogAnalysis;
