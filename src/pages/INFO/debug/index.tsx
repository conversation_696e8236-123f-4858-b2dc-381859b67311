import { INFO_FE_TEXT_MAP } from '@/common/constants/I18nTextMap';
import PageHeader from '@/components/page-header';
import { FC } from 'react';
import styles from './index.module.scss';
import { observer } from 'mobx-react';
import BackNav from '@/components/WindowForm/BackNav';
import useInfoDebug from './use-info-debug';
import { TabPane, Tabs, Typography } from '@hi-design/ui';
import { INFO_DEBUG_TAB } from '@/common/constants/info';
import { useStores } from '@/stores';
import { LogAnalysis } from './log-analysis';
import { LiveDebug } from './live-debug';

const InfoDebug: FC = () => {
  const { staff, loading } = useStores();

  // 获得人员权限
  const { staffInfoType } = staff || {};
  // 页面loading
  const { setPageLoading } = loading || {};

  const {
    title,
    activeTabKey,
    infoKey,
    infoDesc,
    isLiveDebugFirstLoad,
    onBack,
    onPageTabSwitch,
  } = useInfoDebug({
    permissionPoint: staffInfoType,
    setPageLoading,
  });

  const LiveDebugIcon = INFO_DEBUG_TAB.LIVE_DEBUG.icon;
  const LogAnalysisIcon = INFO_DEBUG_TAB.LOG_ANALYSIS.icon;
  return (
    <div className={styles.page}>
      <BackNav
        onBack={onBack}
        style={{ marginTop: 0 }}
        title={INFO_FE_TEXT_MAP.Back_Tips}
      />
      <PageHeader
        title={title}
        description={
          infoKey ? (
            <div className={styles.desc}>
              <div className={styles.infoKeyContainer}>
                <Typography.Text type="tertiary">Info Key: </Typography.Text>
                <Typography.Text
                  className={styles.infoKey}
                  type="tertiary"
                  copyable
                  ellipsis={{ showTooltip: true }}
                >
                  {infoKey}
                </Typography.Text>
              </div>
              <div>
                <Typography.Text
                  className={styles.infoDesc}
                  type="tertiary"
                  ellipsis={{ showTooltip: true }}
                >
                  {infoDesc}
                </Typography.Text>
              </div>
            </div>
          ) : null
        }
      />
      <div className={styles.content}>
        <Tabs activeKey={activeTabKey} onChange={onPageTabSwitch}>
          <TabPane
            className={styles.tabPane}
            tab={
              <span>
                <LiveDebugIcon />
                {INFO_DEBUG_TAB.LIVE_DEBUG.name}
              </span>
            }
            itemKey={INFO_DEBUG_TAB.LIVE_DEBUG.key}
          >
            <LiveDebug isFirstLoad={isLiveDebugFirstLoad} />
          </TabPane>
          <TabPane
            className={styles.tabPane}
            tab={
              <span>
                <LogAnalysisIcon />
                {INFO_DEBUG_TAB.LOG_ANALYSIS.name}
              </span>
            }
            itemKey={INFO_DEBUG_TAB.LOG_ANALYSIS.key}
          >
            <LogAnalysis />
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default observer(InfoDebug);
