import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { INFO_DEBUG_TAB } from '@/common/constants/info';
import { RouterPath } from '@/const';
import { infoServiceClient, PermissionPoint } from '@/http_idl/infoService';
import { useStores } from '@/stores';
import { useHistory } from '@edenx/runtime/router-v5';
import { Toast } from '@hi-design/ui';
import queryString from 'query-string';
import { useEffect, useRef, useState } from 'react';

export interface UseInfoDebugResult {
  title: string;
  activeTabKey: string;
  infoID: string;
  infoKey: string;
  infoName: string;
  infoDesc: string;
  isLiveDebugFirstLoad: boolean;
  onBack: () => void;
  onPageTabSwitch: (key: string) => void;
}

export interface IUseInfoDebugProps {
  permissionPoint: PermissionPoint;
  setPageLoading: (loading: boolean) => void;
}

const useInfoDebug = (props: IUseInfoDebugProps): UseInfoDebugResult => {
  const { permissionPoint, setPageLoading } = props;
  const history = useHistory();
  const query = queryString.parse(history.location.search) || {};
  const { id, tab } = query;

  const activeKey = (tab as string) || INFO_DEBUG_TAB.LIVE_DEBUG.key;

  const { info } = useStores();
  const { setCurrentInfo } = info || {};

  const [infoDetail, setInfoDetail] = useState<null | {
    key: string;
    name: string;
    desc: string;
  }>(null);

  const firstLiveDebugLoadRef = useRef<boolean>();
  const [isLiveDebugFirstLoad, setIsLiveDebugFirstLoad] = useState(false);

  useEffect(() => {
    if (activeKey === INFO_DEBUG_TAB.LIVE_DEBUG.key) {
      if (firstLiveDebugLoadRef.current === undefined) {
        firstLiveDebugLoadRef.current = true;
        setIsLiveDebugFirstLoad(true);
      }
    }
  }, [activeKey]);

  useEffect(() => {
    const unListen = history.listen(location => {
      // 当路由切换到其他页面时，清空当前info详情
      if (!location.pathname.includes(RouterPath.INFO_DEBUG)) {
        setCurrentInfo(null);
      }
    });

    return () => {
      unListen();
    };
  }, []);

  /**
   * @description: 获取info列表数据
   */
  const getInfoDetailByID = async (id: string): Promise<void> => {
    setPageLoading(true);
    try {
      const result = await infoServiceClient.GetInfoDataList({
        selectMap: { id },
        permissionPoint,
        pageNum: 1,
        pageSize: 20,
      });

      if (result?.BaseResp?.StatusCode !== 0) {
        Toast.warning('Invalid info ID. No detail data returned');
        return;
      }
      const { infoDataList } = result;
      if (infoDataList?.length) {
        const detailRaw = infoDataList[0];
        if (detailRaw?.length) {
          const key = detailRaw.filter(item => item.key === 'infoKey')[0]
            ?.value;
          const name = detailRaw.filter(item => item.key === 'name')[0]?.value;
          const desc = detailRaw.filter(item => item.key === 'infoDesc')[0]
            ?.value;
          const apiId = detailRaw.filter(item => item.key === 'apiId')[0]
            ?.value;
          setInfoDetail({
            key,
            name,
            desc,
          });
          setCurrentInfo({
            infoKey: key,
            infoName: name,
            infoDesc: desc,
            infoID: id,
            apiID: apiId,
          });
        }
      }
    } catch (err) {
      Toast.error('Failed to get info detail');
      console.error('getInfoDetailByID err:', err);
    } finally {
      setPageLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      getInfoDetailByID(id as string);
    }
  }, [id]);

  return {
    title: infoDetail?.name
      ? `${INFO_DEBUG_TEXT_MAP.TITLE(infoDetail?.name || '')}`
      : '',
    infoID: id as string,
    infoKey: infoDetail?.key || '',
    infoName: infoDetail?.name || '',
    infoDesc: infoDetail?.desc || '',
    activeTabKey: activeKey,
    isLiveDebugFirstLoad,
    onBack: () => history.push('/info/list'),
    onPageTabSwitch: (key: string) => {
      const stringfied = queryString.stringify({
        ...query,
        tab: key,
      });
      history.replace(`${RouterPath.INFO_DEBUG}?${stringfied}`);
    },
  };
};

export default useInfoDebug;
