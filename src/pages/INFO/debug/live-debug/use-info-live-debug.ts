import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useStores } from '@/stores';
import { ExecResult, InfoDetail } from '@/types/info-debug';
import { useHistory } from '@edenx/runtime/router-v5';
import queryString from 'query-string';
import { Toast } from '@hi-design/ui';
import { DataFromLogIdForm } from '@/components/live-debug/log-data-form';
import { infoServiceRpcService } from '@/common/http/infoServiceRPCService';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { ErrorResponse, isRequestCancelled } from '@/common/http';
import { dataMarketService } from '@/common/http/dataMarketService';
import { QueryByInfoKeysV2Response } from '@/bam-auto-generate/dataMarket';
import { isFunction } from 'lodash-es';
import {
  INFO_DEBUG_TAB,
  LIVE_DEBUG_PSM,
  ServiceRegion,
} from '@/common/constants/info';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';
import { RouterPath } from '@/const';

export interface IUseInfoLiveDebugProps {
  isFirstLoad: boolean;
  onParamsFromLogSuccess?: (params: Record<string, string>) => void;
}

export interface UseInfoLiveDebugResult {
  hasInfo: boolean;
  isResultLoading: boolean;
  isLogLoading: boolean;
  isRefreshLoading: boolean;
  initDataFetchError: string; // 初始化拉取入参配置报错内容
  currentInfo: InfoDetail | null;
  requestDataConfig: Record<string, info_service.InfoField>;
  dataLogFormValues: DataFromLogIdForm;
  requestDataStr: string; // 序列化后的请求入参，用于json渲染
  initRequestDataFormValues: Record<string, string>; // 初始化表单内容，从日志或接口返回，用于初始化表单值
  execResult: ExecResult | null; // 请求执行结果
  // 监听log id获取请求数据表单，更新query
  onDataLogFormChange: (values: DataFromLogIdForm) => void;
  // log id获取请求数据表单提交
  handleGetRequestDataFromLogId: () => void;
  // 取消log id获取请求数据表单提交
  handleCancelGetDataFromLogId: () => void;
  // 监听请求数据表单变化，同步数据到requestDataFormValues
  onRequestDataFormChange: (values: Record<string, string>) => void;
  // 刷新请求数据
  handleRefreshRequestData: () => void;
  // 取消刷新请求数据
  handleCancelRefreshRequestData: () => void;
  // 执行测试请求
  handleExecuteRequest: (values: Record<string, string>) => void;
  // log id 跳转
  handleClickLogId: (logId: string) => void;
}

// eslint-disable-next-line max-lines-per-function
export const useInfoLiveDebug = (
  props: IUseInfoLiveDebugProps,
): UseInfoLiveDebugResult => {
  const { isFirstLoad, onParamsFromLogSuccess } = props;
  const { info, unified, loading } = useStores();
  const { currentInfo } = info || {};
  const { agent } = unified || {};
  const { setPageLoading } = loading || {};
  const history = useHistory();
  const query = queryString.parse(history.location.search) || {};
  const { id: currentPageId } = query;

  // State for request data configuration
  const [requestDataConfig, setRequestDataConfig] = useState<
    Record<string, info_service.InfoField>
  >({});

  // State for form values
  const [dataLogFormValues, setDataLogFormValues] = useState<DataFromLogIdForm>(
    {
      logID: (query.dataLogId as string) || '',
      region: ServiceRegion.ROW, // Default to ROW and cannot be changed
    },
  );

  // State for request data form values
  const [requestDataFormValues, setRequestDataFormValues] = useState<
    Record<string, string>
  >({});

  const [initRequestDataFormValues, setInitRequestDataFormValues] = useState<
    Record<string, string>
  >({});

  // State for loading state
  const [isResultLoading, setIsResultLoading] = useState<boolean>(false);
  const [isLogLoading, setIsLogLoading] = useState<boolean>(false);
  const [isRefreshLoading, setIsRefreshLoading] = useState<boolean>(false);
  const [initDataFetchError, setInitDataFetchError] = useState<string>('');

  // State for execution result
  const [execResult, setExecResult] = useState<ExecResult | null>(null);

  const refreshRequestRef = useRef<AbortController>();
  const getFromLogRequestRef = useRef<AbortController>();

  const requestDataStr = useMemo(() => {
    const reqBody = {
      Psm: INFO_DEBUG_TEXT_MAP.REPLACE_PSM_TIP,
      Variables: requestDataFormValues,
      InfoKeys: [currentInfo?.infoKey],
    };
    return JSON.stringify(reqBody, null, 2);
  }, [requestDataFormValues, currentInfo?.infoKey]);

  // Function to fetch initial test data
  const fetchInitTestData = useCallback(async () => {
    if (!currentInfo?.apiID || !currentInfo?.infoKey) {
      return;
    }

    setPageLoading(true);
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const req = {
        apiId: currentInfo.apiID,
        infoKey: currentInfo.infoKey,
        operateUserEmail: `${agent?.Email || ''},${agent?.UserName || ''}`,
        newData: JSON.stringify({ apiId: currentInfo.apiID }),
      };

      const res = await infoServiceRpcService.GetInfoInitTestData(req);

      if (!res?.infoTestDataSuccess) {
        setInitDataFetchError(
          res.infoTestDataTip ||
            'Server Error: Failed to fetch initial test data',
        );
        Toast.error(
          res.infoTestDataTip ||
            'Server Error: Failed to fetch initial test data',
        );
        return;
      }

      const configData: Record<string, info_service.InfoField> = {};
      const formValues: Record<string, string> = {};

      res?.infoVariables?.forEach(field => {
        // Add to config
        if (!field.field) {
          return;
        }

        configData[field.field] = {
          field: field.field,
          fieldName: field.fieldName || '',
          fieldDesc: field.fieldDesc || '',
          fieldTestVal: field.fieldTestVal || '',
          fieldOfflineTestVal: field.fieldOfflineTestVal || '',
          fieldType: field.fieldType || '',
          fieldIsRequired: field.fieldIsRequired || false,
        };

        const value =
          field.fieldOfflineTestVal?.trim() ||
          field.fieldTestVal?.trim() ||
          undefined;

        // Set initial form values (prefer offline value, then test value, then empty)
        if (value) {
          formValues[field.field] = value;
        }
      });

      // Update state
      setRequestDataConfig(configData);
      setInitRequestDataFormValues(formValues);
      setRequestDataFormValues(formValues);
    } catch (error) {
      setInitDataFetchError('Failed to fetch initial test data');
      const err = error as ErrorResponse;
      Toast.error(
        `Failed to fetch initial test data. ${err.statusMsg || ''} ${err.error || ''}`,
      );
      console.error('Error fetching init test data:', error);
    } finally {
      setPageLoading(false);
    }
  }, [currentInfo?.apiID, currentInfo?.infoKey, agent]);

  // Function to handle data log form changes
  const onDataLogFormChange = useCallback(
    (values: DataFromLogIdForm) => {
      setDataLogFormValues(values);

      // Update URL query parameters
      const newQuery = {
        ...query,
        dataLogId: values.logID,
        dataRegion: values.region,
      };

      const queryStr = queryString.stringify(newQuery);
      history.replace(`${history.location.pathname}?${queryStr}`);
    },
    [history, query],
  );

  // Function to handle getting request data from log ID
  const handleGetRequestDataFromLogId = useCallback(async () => {
    if (!dataLogFormValues.logID) {
      Toast.error('Log ID is required');
      return;
    }

    if (!currentInfo?.apiID || !currentInfo?.infoKey) {
      Toast.error('Info Key is missing');
      return;
    }

    if (getFromLogRequestRef.current) {
      return;
    }
    const abortController = new AbortController();
    getFromLogRequestRef.current = abortController;

    setIsLogLoading(true);
    try {
      const res = await infoServiceRpcService.GetInfoTestDataFromLogID(
        {
          logId: dataLogFormValues.logID,
          infoKey: currentInfo?.infoKey,
        },
        {
          signal: abortController.signal,
        },
      );

      if (res?.infoTestDataSuccess && res?.infoTestData) {
        // Filter keys using requestDataConfig
        const filteredData: Record<string, string> = {};

        Object.keys(requestDataConfig).forEach(key => {
          if (key in (res?.infoTestData || {})) {
            filteredData[key] = res?.infoTestData?.[key] || '';
          } else {
            if (requestDataFormValues[key] !== undefined) {
              // Keep existing value if not in the response
              filteredData[key] = requestDataFormValues[key] || '';
            }
          }
        });

        if (isFunction(onParamsFromLogSuccess)) {
          onParamsFromLogSuccess(filteredData);
        }

        setRequestDataFormValues(filteredData);
        setInitRequestDataFormValues(filteredData);
      } else {
        // Show error message
        Toast.error(res?.infoTestDataTip || 'Failed to get data from log ID');
      }
    } catch (error) {
      // Check if the request was cancelled
      if (isRequestCancelled(error)) {
        Toast.info(COMMON_FE_TEXT_MAP.REQUEST_CANCELLED);
        console.log('Request was cancelled by user');
        return;
      }

      const err = error as ErrorResponse;
      Toast.error(
        `Error getting data from log ID. ${err.statusMsg || ''} ${err.error || ''}`,
      );
      console.error('Error getting data from log ID:', error);
    } finally {
      getFromLogRequestRef.current = undefined;
      setIsLogLoading(false);
    }
  }, [dataLogFormValues, requestDataConfig, requestDataFormValues]);

  const handleCancelGetDataFromLogId = useCallback(() => {
    if (getFromLogRequestRef.current) {
      getFromLogRequestRef.current.abort();
      getFromLogRequestRef.current = undefined;
      setIsLogLoading(false);
    }
  }, []);

  // Function to handle request data form changes
  const onRequestDataFormChange = useCallback(
    (values: Record<string, string>) => {
      try {
        const newData = JSON.parse(JSON.stringify(values));
        setRequestDataFormValues(newData);
      } catch (error) {
        console.error('Error parsing form values:', error);
      }
    },
    [],
  );

  // Function to refresh request data
  const handleRefreshRequestData = useCallback(async () => {
    try {
      if (refreshRequestRef.current) {
        return;
      }
      const abortController = new AbortController();
      refreshRequestRef.current = abortController;

      setIsRefreshLoading(true);

      const req: info_service.GetInfoTestDataRequest = {
        newData: JSON.stringify({ apiId: currentInfo?.apiID }),
        operateUserMail: `${agent?.Email || ''},${agent?.UserName || ''}`,
        checkLog: true,
      };

      const res = await infoServiceRpcService.GetInfoTestData(req, {
        signal: abortController.signal,
      });

      if (res?.infoTestDataSuccess) {
        // Filter keys using requestDataConfig
        const filteredData: Record<string, string> = {};

        Object.keys(requestDataConfig).forEach(key => {
          if (key in (res?.infoTestData || {})) {
            filteredData[key] = res?.infoTestData?.[key] || '';
          } else {
            if (filteredData[key]) {
              filteredData[key] = '';
            }
          }
        });

        setRequestDataFormValues(filteredData);
        setInitRequestDataFormValues(filteredData);
      } else {
        // Show error message
        Toast.error(res?.infoTestDataTip || 'Failed to get data from log ID');
      }
    } catch (error) {
      // Check if the request was cancelled
      if (isRequestCancelled(error)) {
        Toast.info(COMMON_FE_TEXT_MAP.REQUEST_CANCELLED);
        console.log('Request was cancelled by user');
        return;
      }

      const err = error as ErrorResponse;
      Toast.error(
        `Failed to refresh test data. ${err.statusMsg || ''} ${err.error || ''}`,
      );
      console.error('Error refreshing test data:', error);
    } finally {
      refreshRequestRef.current = undefined;
      setIsRefreshLoading(false);
    }
  }, [currentInfo, agent, requestDataConfig, requestDataFormValues]);

  const handleCancelRefreshRequestData = useCallback(() => {
    if (refreshRequestRef.current) {
      refreshRequestRef.current.abort();
      refreshRequestRef.current = undefined;
      setIsRefreshLoading(false);
    }
  }, []);

  // Function to execute test request
  const handleExecuteRequest = useCallback(
    async (values: Record<string, string>) => {
      if (!currentInfo?.infoKey) {
        Toast.error('Info Key is missing');
        return;
      }

      try {
        setIsResultLoading(true);
        const req = {
          Variables: values,
          InfoKeys: [currentInfo?.infoKey],
          Psm: 'info_management_i18n_client',
        };

        const res = await dataMarketService.QueryByInfoKeysV2(req);

        const resultResp: ExecResult = {
          status: 'success',
          errorMsg: [],
          logId:
            (res as QueryByInfoKeysV2Response & { logId: string })?.logId || '', // TODO: 从请求返回里拿
          data: '',
          rawResp: '',
        };

        resultResp.data =
          res?.QueryResults?.filter(
            item => item.InfoKey === currentInfo?.infoKey,
          )?.[0]?.InfoValue || '';

        if (res?.Errors?.length) {
          resultResp.status = 'fail';
          resultResp.errorMsg = res.Errors;
        } else {
          if (resultResp.data === undefined) {
            resultResp.status = 'fail';
            resultResp.errorMsg = ['No data returned for the info key'];
          } else {
            resultResp.status = 'success';
            resultResp.errorMsg = res?.Errors || [];
          }
        }
        resultResp.rawResp = JSON.stringify(res.QueryResults, null, 2);

        setExecResult(resultResp);
      } catch (error) {
        const err = error as ErrorResponse;
        Toast.error(
          `Failed to execute the test request. ${err.statusMsg || ''} ${err.error || ''}`,
        );
        console.error('Failed to execute the test request:', error);
      } finally {
        setIsResultLoading(false);
      }
    },
    [currentInfo?.infoKey],
  );

  const handleClickLogId = useCallback((logId: string) => {
    if (!logId) {
      return;
    }
    const newQuery = {
      ...query,
      logID: logId,
      tab: INFO_DEBUG_TAB.LOG_ANALYSIS.key,
      psm: LIVE_DEBUG_PSM,
      region: ServiceRegion.ROW,
    };

    history.push(`${RouterPath.INFO_DEBUG}?${queryString.stringify(newQuery)}`);
  }, []);

  // Fetch initial data when component mounts or currentInfo changes
  // Only fetch if currentInfo matches the current page ID to prevent stale data requests
  useEffect(() => {
    if (
      currentInfo?.apiID &&
      currentInfo?.infoKey &&
      currentInfo?.infoID === currentPageId &&
      isFirstLoad
    ) {
      fetchInitTestData();
    }
  }, [
    currentInfo?.infoKey,
    currentInfo?.apiID,
    currentInfo?.infoID,
    currentPageId,
    fetchInitTestData,
    isFirstLoad,
  ]);

  return {
    currentInfo,
    hasInfo: !!currentInfo && !!currentInfo?.infoKey && !!currentInfo?.apiID,
    isResultLoading,
    isLogLoading,
    isRefreshLoading,
    initDataFetchError,
    requestDataConfig,
    dataLogFormValues,
    initRequestDataFormValues,
    requestDataStr,
    execResult,
    onDataLogFormChange,
    handleGetRequestDataFromLogId,
    handleCancelGetDataFromLogId,
    onRequestDataFormChange,
    handleRefreshRequestData,
    handleCancelRefreshRequestData,
    handleExecuteRequest,
    handleClickLogId,
  };
};
