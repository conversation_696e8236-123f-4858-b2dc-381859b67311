import { FC } from 'react';
import { useInfoLiveDebug } from './use-info-live-debug';
import {
  Button,
  Empty,
  Space,
  Toast,
  Tooltip,
  Typography,
} from '@hi-design/ui';
import {
  IllustrationIdle,
  IllustrationIdleDark,
} from '@hi-design/ui-illustrations';
import styles from './index.module.scss';
import LogDataForm from '@/components/live-debug/log-data-form';
import TestRequestParams from '@/components/live-debug/test-request-params';
import { IconRefresh, IconStop } from '@hi-design/ui-icons';
import { ExecutionResult } from '@/components/live-debug/execution-result';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';

export interface LiveDebugProps {
  isFirstLoad: boolean;
}

export const LiveDebug: FC<LiveDebugProps> = props => {
  const { isFirstLoad } = props;
  const onParamsFromLogSuccess = (params: Record<string, string>) => {
    const numOfParams = Object.keys(params).length;
    if (!numOfParams) {
      Toast.warning(INFO_DEBUG_TEXT_MAP.PARAMS_NOT_FOUND_FROM_LOG);
      return;
    } else {
      Toast.success(
        INFO_DEBUG_TEXT_MAP.PARAMS_FETCH_SUCCESS_FROM_LOG(numOfParams),
      );
    }
  };

  const {
    hasInfo,
    isResultLoading,
    isLogLoading,
    isRefreshLoading,
    dataLogFormValues,
    requestDataConfig,
    requestDataStr,
    initRequestDataFormValues,
    execResult,
    onDataLogFormChange,
    handleGetRequestDataFromLogId,
    handleCancelGetDataFromLogId,
    onRequestDataFormChange,
    handleRefreshRequestData,
    handleExecuteRequest,
    handleClickLogId,
    handleCancelRefreshRequestData,
  } = useInfoLiveDebug({
    isFirstLoad,
    onParamsFromLogSuccess,
  });

  if (!hasInfo) {
    return (
      <Empty
        className={styles.empty}
        image={<IllustrationIdle style={{ width: 150, height: 150 }} />}
        darkModeImage={
          <IllustrationIdleDark style={{ width: 150, height: 150 }} />
        }
        title="Info Key is Empty"
        description="Please select an Info Key first"
      />
    );
  }

  return (
    <div className={styles.liveDebug}>
      <section>
        <LogDataForm
          handleGetRequestDataFromLogId={handleGetRequestDataFromLogId}
          handleCancelGetDataFromLogId={handleCancelGetDataFromLogId}
          isLogLoading={isLogLoading}
          initialValues={dataLogFormValues}
          onDataLogFormChange={onDataLogFormChange}
        />
      </section>
      <section>
        <div className={styles.logDataHeader}>
          <Typography.Title heading={6}>
            {INFO_DEBUG_TEXT_MAP.REQUEST_PARAMS}
          </Typography.Title>
          <Space>
            <Tooltip content={INFO_DEBUG_TEXT_MAP.REFRESH_PARAMS_TIP}>
              <Button
                loading={isRefreshLoading}
                icon={<IconRefresh />}
                onClick={handleRefreshRequestData}
              >
                {INFO_DEBUG_TEXT_MAP.REFRESH_PARAMS}
              </Button>
            </Tooltip>
            {isRefreshLoading ? (
              <Button
                theme="borderless"
                type="danger"
                icon={<IconStop />}
                onClick={handleCancelRefreshRequestData}
              >
                {COMMON_FE_TEXT_MAP.Cancel}
              </Button>
            ) : null}
          </Space>
        </div>
        <TestRequestParams
          requestDataConfig={requestDataConfig}
          requestDataStr={requestDataStr}
          initRequestDataFormValues={initRequestDataFormValues}
          isResultLoading={isResultLoading}
          isRefreshLoading={isRefreshLoading}
          onRequestDataFormChange={onRequestDataFormChange}
          handleExecuteRequest={handleExecuteRequest}
        />
      </section>
      <section>
        <Typography.Title heading={6} className={styles.sectionHeader}>
          {INFO_DEBUG_TEXT_MAP.RUN_DATA}
        </Typography.Title>
        <ExecutionResult
          execResult={execResult}
          isResultLoading={isResultLoading}
          handleClickLogId={handleClickLogId}
        />
      </section>
    </div>
  );
};
