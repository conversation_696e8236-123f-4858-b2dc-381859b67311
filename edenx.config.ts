import { appTools, defineConfig } from '@edenx/app-tools';
import { routerV5Plugin } from '@edenx/plugin-router-v5';
import { builderPluginHiuiWebpack } from '@hi-design/builder-plugin';
import { slardarWebPlugin } from '@edenx/plugin-slardar-web';
import { ArgusWebpackPlugin } from '@ies/argus-webpack-plugin';

const BYTEGOOFY = 'moc.yfoogetyb'.split('').reverse().join('');
const BYTEDANCE = 'ten.ecnadetyb'.split('').reverse().join('');
const BYTED = 'gro.detyb'.split('').reverse().join('');
const BYTESCM = 'moc.mcsetyb'.split('').reverse().join('');
const ZIJIEAPI = 'moc.ipaeijiz'.split('').reverse().join('');
const VOLCCDN = 'moc.ndcclov'.split('').reverse().join('');
const CSPCDNDOMAIN = 'moc.sotetyb.psc-3fl'.split('').reverse().join('');
const BYTEDNSDOC = 'moc.codsndetyb.citats-3fl'.split('').reverse().join('');

const SLARDAR_BID = 'helpdesk_info_management_i18n';

// https://edenx.bytedance.net/configure/app/usage
export default defineConfig({
  devtools: { sw: false },
  source: {
    entries: {
      main: {
        entry: './src/index.tsx',
        disableMount: true,
      },
    },
    // 禁用默认入口扫描
    disableDefaultEntries: true,
    define: {
      // 读取 SCM 构建版本
      'process.env.BUILD_VERSION': JSON.stringify(process.env.BUILD_VERSION),
      'process.env.BUILD_TYPE': JSON.stringify(process.env.BUILD_TYPE),
      'process.env.CDN_OUTER_CN': JSON.stringify(process.env.CDN_OUTER_CN),
      'process.env.CDN_INNER_CN': JSON.stringify(process.env.CDN_INNER_CN),
      'process.env.CDN_INNER_VA': JSON.stringify(process.env.CDN_INNER_VA),
      'process.env.CDN_REWRITE_INNER_CN': JSON.stringify(
        process.env.CDN_REWRITE_INNER_CN,
      ),
      'process.env.CDN_REWRITE_INNER_SG': JSON.stringify(
        process.env.CDN_REWRITE_INNER_SG,
      ),
      'process.env.BUILD_TOKEN': JSON.stringify(process.env.BUILD_TOKEN),
    },
  },
  runtime: {
    router: {
      // 兼容V5语法
      mode: 'react-router-5',
    },
  },
  tools: {
    webpack: {
      output: {
        libraryTarget: 'umd',
      },
    },
    bundlerChain: chain => {
      chain.plugin('ArgusWebpackPlugin').use(ArgusWebpackPlugin, [
        {
          filter: {
            enable: process.env.NODE_ENV === 'production',
            log: true,
            customFilter: [
              {
                target: BYTEGOOFY, // 需要替换的字符串（必填）
                reg: /bytegoofy\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: BYTEDANCE, // 需要替换的字符串（必填）
                reg: /bytedance\.net/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: BYTED, // 需要替换的字符串（必填）
                reg: /byted\.org/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: BYTESCM,
                reg: /bytescm\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: ZIJIEAPI,
                reg: /zijieapi\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: VOLCCDN,
                reg: /volccdn\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: 'ibytedapm.com',
                reg: /ibytedapm\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: CSPCDNDOMAIN,
                reg: /lf3-csp\.bytetos\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
              {
                target: BYTEDNSDOC,
                reg: /lf3-static\.bytednsdoc.com\.com/g, // 正则匹配需要过滤的关键字（必填）
              },
            ],
            defaultFilterKey: '_', // 未指定替换字符串时，使用的默认占位符
            isFilterInternal: true, // 是否过滤 SCM 构建路径等内部信息
            isFilterBuffer: false, // 是否过滤 Buffer 类型的 source
          },
          common: {
            report: {
              bid: SLARDAR_BID,
              region: 'maliva',
            },
            printOptions: process.env.NODE_ENV === 'production',
          },
        },
      ]);
    },
  },
  plugins: [
    appTools({
      bundler: 'webpack', // Set to 'webpack' to enable webpack
    }),
    routerV5Plugin(),
    slardarWebPlugin({
      bid: SLARDAR_BID, // 上传 sourcemap 的 bid
      region: 'i18n', // 上传区域
      release: process.env.BUILD_VERSION, // 版本，需要与 runtime.slardar 一致
      clearAfterUpload: true, // 同 clear_after_upload
      batch: true, // 开启异步上传
    }),
  ],
  builderPlugins: [
    builderPluginHiuiWebpack({
      prefixCls: 'united_info',
      theme: '@semi-bot/semi-theme-bytehi-semi2',
      extract: true,
    }),
  ],
  output: {
    disableTsChecker: true,
  },
  dev: {
    port: Number(process.env.DEV_CLIENT_PORT),
  },
  deploy: {
    region: process.env.MY_REGION,
  },
});
